'use client'

import React, { useState } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useDrivers } from '@/hooks/useDrivers'
import { DriverStats } from '@/components/dashboard/DriverStats'
import { DriverFilters } from '@/components/dashboard/DriverFilters'
import { DriverTable } from '@/components/dashboard/DriverTable'
import { CreateDriverModal } from '@/components/dashboard/CreateDriverModal'
import { Plus } from 'lucide-react'
import { DriverDocument } from '@/types/database'
import { CreateDriverData } from '@/services/DriverService'
import { toast } from 'react-hot-toast'

export default function DriversPage() {
  const { userProfile } = useAuth()
  const {
    drivers,
    loading,
    error,
    stats,
    filters,
    hasMore,
    setFilters,
    searchDrivers,
    createDriver,
    updateDriver,
    deleteDriver,
    verifyDriver,
    updateDriverStatus,
    loadMoreDrivers,
    refreshDrivers,
    exportDrivers,
    clearError
  } = useDrivers()

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [showVerifyModal, setShowVerifyModal] = useState(false)
  const [selectedDriver, setSelectedDriver] = useState<DriverDocument | null>(null)

  // Check permissions
  if (!userProfile) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading drivers...</p>
        </div>
      </div>
    )
  }

  // Check if user has permission to access drivers
  const hasDriverAccess = userProfile.roles.includes('admin') || userProfile.roles.includes('office_manager')

  if (!hasDriverAccess) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Access Denied</h3>
          <p className="text-gray-600">You don't have permission to access driver management.</p>
        </div>
      </div>
    )
  }

  // Handle driver actions
  const handleViewDriver = (driver: DriverDocument) => {
    setSelectedDriver(driver)
    setShowViewModal(true)
  }

  const handleEditDriver = (driver: DriverDocument) => {
    setSelectedDriver(driver)
    setShowEditModal(true)
  }

  const handleDeleteDriver = async (driver: DriverDocument) => {
    if (window.confirm(`Are you sure you want to delete driver ${driver.personalInfo.firstName} ${driver.personalInfo.lastName}?`)) {
      try {
        await deleteDriver(driver.id)
        toast.success('Driver deleted successfully')
      } catch (error) {
        toast.error('Failed to delete driver')
      }
    }
  }

  const handleVerifyDriver = async (driver: DriverDocument, approved: boolean) => {
    try {
      await verifyDriver(driver.id, approved)
      toast.success(`Driver ${approved ? 'approved' : 'rejected'} successfully`)
    } catch (error) {
      toast.error(`Failed to ${approved ? 'approve' : 'reject'} driver`)
    }
  }

  const handleToggleStatus = async (driver: DriverDocument) => {
    try {
      const newOnlineStatus = !driver.status.online
      await updateDriverStatus(driver.id, newOnlineStatus, driver.status.available)
      toast.success(`Driver status updated to ${newOnlineStatus ? 'online' : 'offline'}`)
    } catch (error) {
      toast.error('Failed to update driver status')
    }
  }

  const handleCreateDriver = async (driverData: CreateDriverData) => {
    try {
      await createDriver(driverData)
      toast.success('Driver created successfully')
    } catch (error) {
      toast.error('Failed to create driver')
      throw error
    }
  }

  const handleExport = async () => {
    try {
      const driversData = await exportDrivers()
      // Convert to CSV and download
      const csvContent = convertToCSV(driversData)
      downloadCSV(csvContent, 'drivers-export.csv')
      toast.success('Drivers exported successfully')
    } catch (error) {
      toast.error('Failed to export drivers')
    }
  }

  const convertToCSV = (drivers: DriverDocument[]) => {
    const headers = [
      'ID', 'First Name', 'Last Name', 'Phone', 'Email', 'License Number',
      'Vehicle Make', 'Vehicle Model', 'Vehicle Year', 'Plate Number',
      'Status', 'Verification', 'Rating', 'Total Earnings', 'Completed Trips'
    ]

    const rows = drivers.map(driver => [
      driver.id,
      driver.personalInfo.firstName,
      driver.personalInfo.lastName,
      driver.personalInfo.phone,
      driver.personalInfo.email,
      driver.personalInfo.licenseNumber,
      driver.vehicle.make,
      driver.vehicle.model,
      driver.vehicle.year,
      driver.vehicle.plateNumber,
      driver.status.online ? 'Online' : 'Offline',
      driver.verification.status,
      driver.earnings.rating.average,
      driver.earnings.totalEarnings,
      driver.earnings.completedTrips
    ])

    return [headers, ...rows].map(row => row.join(',')).join('\n')
  }

  const downloadCSV = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', filename)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Drivers Management</h1>
          <p className="text-gray-600 mt-2">Manage Yellow Taxi drivers and their verification status</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-black px-6 py-3 rounded-xl font-semibold hover:from-yellow-600 hover:to-yellow-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
        >
          <Plus className="w-5 h-5 inline mr-2" />
          Add New Driver
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <button
                  onClick={clearError}
                  className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Driver Statistics */}
      <DriverStats stats={stats} loading={loading} />

      {/* Search and Filters */}
      <DriverFilters
        filters={filters}
        onFiltersChange={setFilters}
        onSearch={searchDrivers}
        onExport={handleExport}
        onRefresh={refreshDrivers}
        loading={loading}
      />

      {/* Drivers Table */}
      <DriverTable
        drivers={drivers}
        loading={loading}
        onViewDriver={handleViewDriver}
        onEditDriver={handleEditDriver}
        onDeleteDriver={handleDeleteDriver}
        onVerifyDriver={handleVerifyDriver}
        onToggleStatus={handleToggleStatus}
      />

      {/* Load More Button */}
      {hasMore && !loading && (
        <div className="text-center">
          <button
            onClick={loadMoreDrivers}
            className="bg-white border border-gray-300 rounded-lg px-6 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50 transition-all duration-300"
          >
            Load More Drivers
          </button>
        </div>
      )}

      {/* Modals */}
      {showCreateModal && (
        <CreateDriverModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateDriver}
        />
      )}

      {/* TODO: Add remaining modals */}
      {/*
      {showEditModal && selectedDriver && (
        <EditDriverModal
          isOpen={showEditModal}
          driver={selectedDriver}
          onClose={() => {
            setShowEditModal(false)
            setSelectedDriver(null)
          }}
          onSubmit={handleUpdateDriver}
        />
      )}

      {showViewModal && selectedDriver && (
        <ViewDriverModal
          isOpen={showViewModal}
          driver={selectedDriver}
          onClose={() => {
            setShowViewModal(false)
            setSelectedDriver(null)
          }}
        />
      )}

      {showVerifyModal && selectedDriver && (
        <VerifyDriverModal
          isOpen={showVerifyModal}
          driver={selectedDriver}
          onClose={() => {
            setShowVerifyModal(false)
            setSelectedDriver(null)
          }}
          onVerify={handleVerifyDriver}
        />
      )}
      */}
    </div>
  )
}
