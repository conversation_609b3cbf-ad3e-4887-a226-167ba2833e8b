'use client'

import { useAuth } from '@/hooks/useAuth'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import { Logo } from '@/components/ui/logo'
import { 
  Home, 
  Users, 
  Car, 
  FileText, 
  CreditCard, 
  Settings, 
  BarChart3,
  Shield
} from 'lucide-react'

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: Home },
  { name: 'Users', href: '/users', icon: Users, roles: ['admin', 'office_manager'] },
  { name: 'Drivers', href: '/drivers', icon: Car, roles: ['admin', 'office_manager'] },
  { name: 'Orders', href: '/orders', icon: FileText, roles: ['admin', 'office_manager', 'driver'] },
  { name: 'Payments', href: '/payments', icon: CreditCard, roles: ['admin', 'office_manager'] },
  { name: 'Analytics', href: '/analytics', icon: BarChart3, roles: ['admin', 'office_manager'] },
  { name: 'Settings', href: '/settings', icon: Settings },
]

export function DashboardSidebar() {
  const { userProfile } = useAuth()
  const pathname = usePathname()

  if (!userProfile) {
    return null
  }

  const filteredNavigation = navigation.filter(item => {
    if (!item.roles) return true
    return item.roles.some(role => userProfile.roles.includes(role))
  })

  return (
    <aside className="opacity-0 animate-fade-in-left fixed z-30 inset-y-0 left-0 w-16 md:w-20 glass-effect flex flex-col transition-all duration-500 hover:shadow-2xl hover:w-20 md:hover:w-24 border-r pt-6 pb-6 shadow-2xl items-center">
      <div className="flex flex-col space-y-8">
        {/* Yellow Taxi Logo */}
        <div className="transition-all duration-300 cursor-pointer hover:text-yellow-600 animate-glow text-yellow-500">
          <div className="w-10 h-10 rounded-xl flex items-center justify-center font-bold text-sm tracking-tight bg-gradient-to-br from-yellow-500 to-orange-500 text-black shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110">
            <Logo size="sm" showText={false} />
          </div>
        </div>

        {/* Navigation Items */}
        {filteredNavigation.slice(0, -1).map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'p-3 rounded-xl transition-all duration-300 group transform hover:scale-110',
                isActive
                  ? 'text-yellow-600 bg-white/80 shadow-lg backdrop-blur-sm scale-110'
                  : 'text-gray-400 hover:text-yellow-600 hover:bg-white/50 hover:shadow-lg'
              )}
            >
              <item.icon className="w-5 h-5" strokeWidth={1.5} />
            </Link>
          )
        })}
      </div>

      {/* Settings at Bottom */}
      <div className="mt-auto">
        {filteredNavigation.slice(-1).map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'p-3 rounded-xl transition-all duration-300 group transform hover:scale-110',
                isActive
                  ? 'text-yellow-600 bg-white/80 shadow-lg backdrop-blur-sm scale-110'
                  : 'text-gray-400 hover:text-yellow-600 hover:bg-white/50 hover:shadow-lg'
              )}
            >
              <item.icon className="w-5 h-5" strokeWidth={1.5} />
            </Link>
          )
        })}
      </div>
    </aside>
  )
}